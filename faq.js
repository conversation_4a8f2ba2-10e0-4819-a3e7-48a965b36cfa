// Sistema de FAQ Dinâmico - Logyc Contabilidade
class FAQSystem {
    constructor() {
        this.faqs = this.getFAQData();
        this.currentCategory = 'all';
        this.searchTerm = '';
        this.init();
    }
    
    init() {
        this.createFAQSection();
        this.renderFAQs();
        this.setupEventListeners();
    }
    
    getFAQData() {
        return [
            {
                id: 1,
                category: 'geral',
                question: 'Quais serviços a Logyc Contabilidade oferece?',
                answer: 'Oferecemos abertura de empresas, gestão fiscal e tributária, departamento pessoal, consultoria contábil, regularização empresarial e somos especialistas no Simples Nacional.',
                keywords: ['serviços', 'oferece', 'contabilidade']
            },
            {
                id: 2,
                category: 'precos',
                question: 'Quanto custa a mensalidade contábil?',
                answer: 'Os valores variam conforme o tipo de empresa e faturamento. MEI: R$ 75/mês, Serviços: R$ 285-370/mês, Comércio: R$ 245-390/mês. Use nossa calculadora para um orçamento personalizado.',
                keywords: ['preço', 'valor', 'custo', 'mensalidade']
            },
            {
                id: 3,
                category: 'mei',
                question: 'Vocês atendem MEI?',
                answer: 'Sim! Para MEI nossa mensalidade é R$ 75,00 + R$ 40,00 por funcionário. Cuidamos de toda a parte fiscal, DAS, declarações e orientações.',
                keywords: ['mei', 'microempreendedor', 'individual']
            },
            {
                id: 4,
                category: 'abertura',
                question: 'Como funciona a abertura de empresa?',
                answer: 'Cuidamos de todo o processo: escolha do tipo societário, registro na Junta Comercial, CNPJ, inscrições municipais e estaduais, alvarás e licenças necessárias.',
                keywords: ['abertura', 'abrir', 'empresa', 'cnpj']
            },
            {
                id: 5,
                category: 'documentos',
                question: 'Quais documentos preciso para abrir uma empresa?',
                answer: 'RG, CPF e comprovante de residência dos sócios, contrato de locação ou escritura do imóvel, definição da atividade e capital social.',
                keywords: ['documentos', 'preciso', 'abertura']
            },
            {
                id: 6,
                category: 'troca',
                question: 'Como trocar de contabilidade?',
                answer: 'Facilitamos todo o processo! Solicitamos os arquivos da contabilidade anterior, fazemos a transferência e assumimos todas as obrigações sem complicações para você.',
                keywords: ['trocar', 'mudar', 'transferir', 'contabilidade']
            },
            {
                id: 7,
                category: 'pessoal',
                question: 'Vocês cuidam do departamento pessoal?',
                answer: 'Sim! Fazemos admissões, demissões, folha de pagamento, férias, 13º salário, e-Social, DCTF e todas as obrigações trabalhistas.',
                keywords: ['pessoal', 'funcionários', 'folha', 'pagamento']
            },
            {
                id: 8,
                category: 'fiscal',
                question: 'Quais impostos vocês apuram?',
                answer: 'Apuramos todos os impostos: Simples Nacional, PIS/COFINS, ICMS, ISS, IRPJ, CSLL, retenções e fazemos todas as declarações obrigatórias.',
                keywords: ['impostos', 'fiscal', 'tributário', 'simples']
            },
            {
                id: 9,
                category: 'atendimento',
                question: 'Vocês atendem todo o Brasil?',
                answer: 'Sim! Estamos fisicamente em Curitiba-PR, mas atendemos clientes de todo o Brasil através de tecnologia e atendimento digital.',
                keywords: ['atendimento', 'brasil', 'curitiba', 'digital']
            },
            {
                id: 10,
                category: 'prazo',
                question: 'Qual o prazo para abertura de empresa?',
                answer: 'O prazo varia de 15 a 30 dias úteis, dependendo do tipo de empresa, atividade e órgãos envolvidos. Agilizamos ao máximo o processo.',
                keywords: ['prazo', 'tempo', 'abertura', 'demora']
            },
            {
                id: 11,
                category: 'consultoria',
                question: 'Vocês fazem consultoria empresarial?',
                answer: 'Sim! Oferecemos consultoria tributária, societária, planejamento fiscal e orientações estratégicas para otimizar sua empresa.',
                keywords: ['consultoria', 'orientação', 'planejamento']
            },
            {
                id: 12,
                category: 'contato',
                question: 'Como posso entrar em contato?',
                answer: 'WhatsApp: (41) 98742-7111, através do nosso site ou pelos formulários disponíveis. Respondemos rapidamente!',
                keywords: ['contato', 'whatsapp', 'telefone', 'falar']
            }
        ];
    }
    
    createFAQSection() {
        // Verificar se já existe uma seção FAQ
        let faqSection = document.getElementById('faq-section');
        
        if (!faqSection) {
            faqSection = document.createElement('section');
            faqSection.id = 'faq-section';
            faqSection.className = 'faq-section';
            
            // Inserir antes do footer
            const footer = document.querySelector('.footer');
            if (footer) {
                footer.parentNode.insertBefore(faqSection, footer);
            } else {
                document.body.appendChild(faqSection);
            }
        }
        
        faqSection.innerHTML = `
            <div class="container">
                <div class="faq-header">
                    <h2>Perguntas Frequentes</h2>
                    <p>Encontre respostas rápidas para as dúvidas mais comuns</p>
                </div>
                
                <div class="faq-controls">
                    <div class="faq-search">
                        <input type="text" id="faqSearch" placeholder="Buscar pergunta..." />
                        <button id="faqSearchBtn">🔍</button>
                    </div>
                    
                    <div class="faq-categories">
                        <button class="faq-category active" data-category="all">Todas</button>
                        <button class="faq-category" data-category="geral">Geral</button>
                        <button class="faq-category" data-category="precos">Preços</button>
                        <button class="faq-category" data-category="abertura">Abertura</button>
                        <button class="faq-category" data-category="mei">MEI</button>
                        <button class="faq-category" data-category="fiscal">Fiscal</button>
                    </div>
                </div>
                
                <div class="faq-list" id="faqList">
                    <!-- FAQs serão inseridas aqui -->
                </div>
                
                <div class="faq-footer">
                    <p>Não encontrou sua resposta?</p>
                    <a href="https://wa.me/5541987427111?text=Olá! Tenho uma dúvida que não encontrei no FAQ." 
                       target="_blank" class="btn-faq-contact">Fale Conosco</a>
                </div>
            </div>
        `;
        
        this.setupStyles();
    }
    
    setupStyles() {
        const styles = `
            .faq-section {
                padding: 80px 0;
                background: var(--cinza-claro, #f8f9fa);
            }
            
            .faq-header {
                text-align: center;
                margin-bottom: 3rem;
            }
            
            .faq-header h2 {
                color: var(--azul-escuro, #005aec);
                font-size: 2.5rem;
                margin-bottom: 1rem;
            }
            
            .faq-header p {
                color: var(--cinza-escuro, #666);
                font-size: 1.1rem;
            }
            
            .faq-controls {
                margin-bottom: 2rem;
                display: flex;
                flex-direction: column;
                gap: 1rem;
                align-items: center;
            }
            
            .faq-search {
                display: flex;
                max-width: 400px;
                width: 100%;
                position: relative;
            }
            
            .faq-search input {
                flex: 1;
                padding: 12px 16px;
                border: 1px solid #ddd;
                border-radius: 25px;
                font-size: 16px;
                outline: none;
                transition: border-color 0.3s;
            }
            
            .faq-search input:focus {
                border-color: var(--azul-escuro, #005aec);
            }
            
            .faq-search button {
                position: absolute;
                right: 5px;
                top: 50%;
                transform: translateY(-50%);
                background: var(--azul-escuro, #005aec);
                color: white;
                border: none;
                border-radius: 50%;
                width: 35px;
                height: 35px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background 0.3s;
            }
            
            .faq-search button:hover {
                background: var(--azul-claro, #0593ff);
            }
            
            .faq-categories {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                justify-content: center;
            }
            
            .faq-category {
                background: white;
                border: 1px solid #ddd;
                border-radius: 20px;
                padding: 8px 16px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s;
                white-space: nowrap;
            }
            
            .faq-category:hover,
            .faq-category.active {
                background: var(--azul-escuro, #005aec);
                color: white;
                border-color: var(--azul-escuro, #005aec);
            }
            
            .faq-list {
                max-width: 800px;
                margin: 0 auto;
            }
            
            .faq-item {
                background: white;
                border-radius: 8px;
                margin-bottom: 1rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                transition: all 0.3s ease;
            }
            
            .faq-item:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }
            
            .faq-question {
                padding: 1.5rem;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: 600;
                color: var(--azul-escuro, #005aec);
                transition: background 0.3s;
            }
            
            .faq-question:hover {
                background: #f8f9fa;
            }
            
            .faq-toggle {
                font-size: 1.5rem;
                transition: transform 0.3s;
                color: var(--azul-escuro, #005aec);
            }
            
            .faq-item.open .faq-toggle {
                transform: rotate(45deg);
            }
            
            .faq-answer {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease;
                background: #f8f9fa;
            }
            
            .faq-answer-content {
                padding: 1.5rem;
                color: var(--cinza-escuro, #666);
                line-height: 1.6;
            }
            
            .faq-item.open .faq-answer {
                max-height: 200px;
            }
            
            .faq-footer {
                text-align: center;
                margin-top: 3rem;
                padding: 2rem;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            
            .faq-footer p {
                margin-bottom: 1rem;
                color: var(--cinza-escuro, #666);
                font-size: 1.1rem;
            }
            
            .btn-faq-contact {
                background: var(--verde-claro, #01d800);
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                text-decoration: none;
                font-weight: 600;
                transition: background 0.3s;
                display: inline-block;
            }
            
            .btn-faq-contact:hover {
                background: var(--verde-escuro, #217345);
            }
            
            .faq-no-results {
                text-align: center;
                padding: 3rem;
                color: var(--cinza-escuro, #666);
            }
            
            .faq-no-results h3 {
                margin-bottom: 1rem;
                color: var(--azul-escuro, #005aec);
            }
            
            @media (max-width: 768px) {
                .faq-controls {
                    padding: 0 1rem;
                }
                
                .faq-categories {
                    gap: 0.25rem;
                }
                
                .faq-category {
                    font-size: 12px;
                    padding: 6px 12px;
                }
                
                .faq-question {
                    padding: 1rem;
                    font-size: 14px;
                }
                
                .faq-answer-content {
                    padding: 1rem;
                    font-size: 14px;
                }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }
    
    setupEventListeners() {
        // Busca
        const searchInput = document.getElementById('faqSearch');
        const searchBtn = document.getElementById('faqSearchBtn');
        
        searchInput.addEventListener('input', (e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.renderFAQs();
        });
        
        searchBtn.addEventListener('click', () => {
            this.renderFAQs();
        });
        
        // Categorias
        document.querySelectorAll('.faq-category').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.faq-category').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentCategory = e.target.dataset.category;
                this.renderFAQs();
            });
        });
    }
    
    renderFAQs() {
        const container = document.getElementById('faqList');
        const filteredFAQs = this.filterFAQs();
        
        if (filteredFAQs.length === 0) {
            container.innerHTML = `
                <div class="faq-no-results">
                    <h3>Nenhuma pergunta encontrada</h3>
                    <p>Tente usar outros termos de busca ou entre em contato conosco.</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = filteredFAQs.map(faq => `
            <div class="faq-item" data-id="${faq.id}">
                <div class="faq-question" onclick="this.parentElement.classList.toggle('open')">
                    <span>${faq.question}</span>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <div class="faq-answer-content">
                        ${faq.answer}
                    </div>
                </div>
            </div>
        `).join('');
        
        // Rastrear visualizações
        if (window.trackEvent) {
            window.trackEvent('faq_view', {
                category: this.currentCategory,
                search_term: this.searchTerm,
                results_count: filteredFAQs.length
            });
        }
    }
    
    filterFAQs() {
        return this.faqs.filter(faq => {
            const matchesCategory = this.currentCategory === 'all' || faq.category === this.currentCategory;
            const matchesSearch = !this.searchTerm || 
                faq.question.toLowerCase().includes(this.searchTerm) ||
                faq.answer.toLowerCase().includes(this.searchTerm) ||
                faq.keywords.some(keyword => keyword.includes(this.searchTerm));
            
            return matchesCategory && matchesSearch;
        });
    }
    
    // Método para adicionar FAQ dinamicamente
    addFAQ(faq) {
        this.faqs.push({
            id: Date.now(),
            ...faq
        });
        this.renderFAQs();
    }
    
    // Método para buscar FAQ por ID
    getFAQById(id) {
        return this.faqs.find(faq => faq.id === id);
    }
}

// Inicializar FAQ apenas na página principal
document.addEventListener('DOMContentLoaded', function() {
    // Verificar se estamos na página principal
    if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
        setTimeout(() => {
            window.faqSystem = new FAQSystem();
        }, 1000);
    }
});

// Exportar para uso global
window.FAQSystem = FAQSystem;
