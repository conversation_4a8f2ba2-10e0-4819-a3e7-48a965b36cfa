// Sistema de Automação de Marketing - Logyc Contabilidade
class MarketingAutomation {
    constructor() {
        this.leads = new Map();
        this.campaigns = new Map();
        this.workflows = new Map();
        this.segments = new Map();
        this.triggers = new Map();
        this.analytics = new MarketingAnalytics();
        this.init();
    }
    
    init() {
        this.setupLeadTracking();
        this.setupBehavioralTriggers();
        this.setupEmailSequences();
        this.setupRetargetingCampaigns();
        this.setupLeadScoring();
        this.setupSegmentation();
        this.setupWorkflows();
        this.startAutomation();
    }
    
    setupLeadTracking() {
        this.leadTracker = {
            capture: (source, data) => {
                const leadId = this.generateLeadId();
                const lead = {
                    id: leadId,
                    source: source,
                    data: data,
                    score: 0,
                    stage: 'awareness',
                    created_at: new Date().toISOString(),
                    last_activity: new Date().toISOString(),
                    interactions: [],
                    segments: [],
                    campaigns: []
                };
                
                this.leads.set(leadId, lead);
                this.processNewLead(lead);
                
                return leadId;
            },
            
            update: (leadId, activity) => {
                const lead = this.leads.get(leadId);
                if (!lead) return;
                
                lead.interactions.push({
                    type: activity.type,
                    data: activity.data,
                    timestamp: new Date().toISOString()
                });
                
                lead.last_activity = new Date().toISOString();
                this.updateLeadScore(lead, activity);
                this.checkWorkflowTriggers(lead, activity);
                
                this.leads.set(leadId, lead);
            }
        };
    }
    
    setupBehavioralTriggers() {
        this.behavioralTriggers = {
            // Page visit triggers
            pageVisit: {
                calculator: {
                    condition: (data) => data.page === '/calculadora.html',
                    action: (lead) => this.triggerCalculatorSequence(lead),
                    delay: 0
                },
                pricing: {
                    condition: (data) => data.time_on_page > 60000, // 1 minute
                    action: (lead) => this.triggerPricingInterest(lead),
                    delay: 30000 // 30 seconds
                },
                blog: {
                    condition: (data) => data.page.includes('/blog'),
                    action: (lead) => this.triggerContentEngagement(lead),
                    delay: 0
                }
            },
            
            // Form interaction triggers
            formInteraction: {
                started: {
                    condition: (data) => data.action === 'form_start',
                    action: (lead) => this.triggerFormAbandonmentSequence(lead),
                    delay: 300000 // 5 minutes
                },
                completed: {
                    condition: (data) => data.action === 'form_submit',
                    action: (lead) => this.triggerFormCompletionSequence(lead),
                    delay: 0
                }
            },
            
            // Engagement triggers
            engagement: {
                high: {
                    condition: (lead) => lead.score > 70,
                    action: (lead) => this.triggerHighValueSequence(lead),
                    delay: 0
                },
                returning: {
                    condition: (data) => data.visit_count > 3,
                    action: (lead) => this.triggerReturningVisitorSequence(lead),
                    delay: 0
                }
            }
        };
    }
    
    setupEmailSequences() {
        this.emailSequences = {
            welcome: {
                name: 'Sequência de Boas-vindas',
                trigger: 'lead_capture',
                emails: [
                    {
                        delay: 0,
                        subject: 'Bem-vindo à Logyc Contabilidade! 🎉',
                        template: 'welcome_email',
                        personalization: true
                    },
                    {
                        delay: 86400000, // 1 day
                        subject: 'Descubra como economizar na sua contabilidade',
                        template: 'value_proposition',
                        personalization: true
                    },
                    {
                        delay: 259200000, // 3 days
                        subject: 'Casos de sucesso: empresas que cresceram conosco',
                        template: 'social_proof',
                        personalization: false
                    }
                ]
            },
            
            calculator_abandonment: {
                name: 'Abandono da Calculadora',
                trigger: 'calculator_exit',
                emails: [
                    {
                        delay: 3600000, // 1 hour
                        subject: 'Esqueceu de finalizar seu cálculo? 🧮',
                        template: 'calculator_reminder',
                        personalization: true
                    },
                    {
                        delay: 86400000, // 1 day
                        subject: 'Oferta especial: primeira mensalidade grátis!',
                        template: 'special_offer',
                        personalization: true
                    }
                ]
            },
            
            form_abandonment: {
                name: 'Abandono de Formulário',
                trigger: 'form_abandonment',
                emails: [
                    {
                        delay: 1800000, // 30 minutes
                        subject: 'Precisa de ajuda para finalizar?',
                        template: 'form_help',
                        personalization: true
                    },
                    {
                        delay: 86400000, // 1 day
                        subject: 'Ainda interessado? Vamos conversar!',
                        template: 'personal_outreach',
                        personalization: true
                    }
                ]
            },
            
            nurturing: {
                name: 'Nutrição de Leads',
                trigger: 'low_engagement',
                emails: [
                    {
                        delay: 604800000, // 1 week
                        subject: 'Dicas essenciais para sua empresa',
                        template: 'educational_content',
                        personalization: false
                    },
                    {
                        delay: 1209600000, // 2 weeks
                        subject: 'Webinar gratuito: Gestão Fiscal 2024',
                        template: 'webinar_invitation',
                        personalization: false
                    }
                ]
            }
        };
    }
    
    setupRetargetingCampaigns() {
        this.retargetingCampaigns = {
            facebook: {
                calculator_visitors: {
                    audience: 'visited_calculator',
                    creative: 'calculator_retargeting',
                    budget: 50,
                    duration: 7
                },
                form_abandoners: {
                    audience: 'abandoned_form',
                    creative: 'form_completion',
                    budget: 30,
                    duration: 3
                }
            },
            
            google: {
                search_retargeting: {
                    keywords: ['contabilidade', 'contador', 'mei'],
                    audience: 'website_visitors',
                    creative: 'search_ad',
                    budget: 100,
                    duration: 14
                }
            }
        };
    }
    
    setupLeadScoring() {
        this.leadScoring = {
            actions: {
                page_visit: 5,
                calculator_use: 15,
                form_start: 10,
                form_complete: 25,
                email_open: 3,
                email_click: 8,
                whatsapp_click: 20,
                blog_read: 5,
                return_visit: 10
            },
            
            demographics: {
                company_size: {
                    '1-5': 10,
                    '6-20': 15,
                    '21-50': 20,
                    '50+': 25
                },
                revenue: {
                    'up_to_81k': 5,
                    '81k_to_500k': 15,
                    '500k_to_2m': 20,
                    'above_2m': 25
                }
            },
            
            behavioral: {
                time_on_site: {
                    'under_30s': -5,
                    '30s_to_2m': 0,
                    '2m_to_5m': 10,
                    'above_5m': 20
                },
                pages_visited: {
                    '1': 0,
                    '2-3': 5,
                    '4-6': 10,
                    '7+': 15
                }
            }
        };
    }
    
    setupSegmentation() {
        this.segmentationRules = {
            by_company_type: {
                mei: (lead) => lead.data.company_type === 'mei',
                small_business: (lead) => lead.data.employees <= 10,
                medium_business: (lead) => lead.data.employees > 10 && lead.data.employees <= 50,
                large_business: (lead) => lead.data.employees > 50
            },
            
            by_interest: {
                price_sensitive: (lead) => lead.interactions.some(i => i.type === 'calculator_use'),
                service_focused: (lead) => lead.interactions.some(i => i.type === 'services_page'),
                switching: (lead) => lead.interactions.some(i => i.type === 'switching_form')
            },
            
            by_engagement: {
                hot: (lead) => lead.score > 70,
                warm: (lead) => lead.score > 40 && lead.score <= 70,
                cold: (lead) => lead.score <= 40
            },
            
            by_stage: {
                awareness: (lead) => lead.stage === 'awareness',
                consideration: (lead) => lead.stage === 'consideration',
                decision: (lead) => lead.stage === 'decision',
                customer: (lead) => lead.stage === 'customer'
            }
        };
    }
    
    setupWorkflows() {
        this.workflows = new Map([
            ['new_lead', {
                name: 'Novo Lead',
                trigger: 'lead_created',
                steps: [
                    { action: 'send_welcome_email', delay: 0 },
                    { action: 'add_to_nurturing', delay: 3600000 },
                    { action: 'create_retargeting_audience', delay: 0 }
                ]
            }],
            
            ['calculator_user', {
                name: 'Usuário da Calculadora',
                trigger: 'calculator_used',
                steps: [
                    { action: 'increase_score', params: { points: 15 }, delay: 0 },
                    { action: 'send_pricing_email', delay: 3600000 },
                    { action: 'add_to_hot_leads', delay: 0 }
                ]
            }],
            
            ['form_completer', {
                name: 'Completou Formulário',
                trigger: 'form_completed',
                steps: [
                    { action: 'send_thank_you_email', delay: 0 },
                    { action: 'notify_sales_team', delay: 0 },
                    { action: 'schedule_follow_up', delay: 86400000 }
                ]
            }]
        ]);
    }
    
    processNewLead(lead) {
        // Segment the lead
        this.segmentLead(lead);
        
        // Start appropriate workflows
        this.triggerWorkflow('new_lead', lead);
        
        // Track in analytics
        this.analytics.trackLeadCreation(lead);
        
        // Store lead data
        this.storeLead(lead);
    }
    
    segmentLead(lead) {
        Object.keys(this.segmentationRules).forEach(category => {
            Object.keys(this.segmentationRules[category]).forEach(segment => {
                if (this.segmentationRules[category][segment](lead)) {
                    lead.segments.push(`${category}:${segment}`);
                }
            });
        });
    }
    
    updateLeadScore(lead, activity) {
        const scoreConfig = this.leadScoring.actions;
        const points = scoreConfig[activity.type] || 0;
        
        lead.score += points;
        
        // Update stage based on score
        if (lead.score > 70) {
            lead.stage = 'decision';
        } else if (lead.score > 40) {
            lead.stage = 'consideration';
        }
        
        this.analytics.trackScoreChange(lead, points, activity);
    }
    
    checkWorkflowTriggers(lead, activity) {
        this.workflows.forEach((workflow, workflowId) => {
            if (workflow.trigger === activity.type) {
                this.triggerWorkflow(workflowId, lead, activity);
            }
        });
        
        // Check behavioral triggers
        Object.keys(this.behavioralTriggers).forEach(category => {
            Object.keys(this.behavioralTriggers[category]).forEach(trigger => {
                const triggerConfig = this.behavioralTriggers[category][trigger];
                if (triggerConfig.condition(activity.data || lead)) {
                    setTimeout(() => {
                        triggerConfig.action(lead);
                    }, triggerConfig.delay);
                }
            });
        });
    }
    
    triggerWorkflow(workflowId, lead, activity = null) {
        const workflow = this.workflows.get(workflowId);
        if (!workflow) return;
        
        workflow.steps.forEach(step => {
            setTimeout(() => {
                this.executeWorkflowStep(step, lead, activity);
            }, step.delay);
        });
        
        this.analytics.trackWorkflowTrigger(workflowId, lead);
    }
    
    executeWorkflowStep(step, lead, activity) {
        switch (step.action) {
            case 'send_welcome_email':
                this.sendEmail(lead, 'welcome', step.params);
                break;
            case 'send_pricing_email':
                this.sendEmail(lead, 'pricing_info', step.params);
                break;
            case 'increase_score':
                lead.score += step.params?.points || 10;
                break;
            case 'add_to_nurturing':
                this.addToEmailSequence(lead, 'nurturing');
                break;
            case 'notify_sales_team':
                this.notifySalesTeam(lead);
                break;
            default:
                console.log(`Unknown workflow action: ${step.action}`);
        }
    }
    
    sendEmail(lead, template, params = {}) {
        const emailData = {
            to: lead.data.email,
            template: template,
            personalization: {
                name: lead.data.name || 'Cliente',
                company: lead.data.company || '',
                ...params
            },
            lead_id: lead.id,
            timestamp: new Date().toISOString()
        };
        
        // In a real implementation, this would integrate with an email service
        console.log('Sending email:', emailData);
        
        // Track email sent
        this.analytics.trackEmailSent(lead, template);
        
        // Simulate email delivery
        this.simulateEmailDelivery(emailData);
    }
    
    simulateEmailDelivery(emailData) {
        // Simulate email open (70% open rate)
        if (Math.random() < 0.7) {
            setTimeout(() => {
                this.trackEmailOpen(emailData.lead_id, emailData.template);
            }, Math.random() * 3600000); // Random time within 1 hour
        }
        
        // Simulate email click (20% click rate)
        if (Math.random() < 0.2) {
            setTimeout(() => {
                this.trackEmailClick(emailData.lead_id, emailData.template);
            }, Math.random() * 7200000); // Random time within 2 hours
        }
    }
    
    trackEmailOpen(leadId, template) {
        const lead = this.leads.get(leadId);
        if (lead) {
            this.leadTracker.update(leadId, {
                type: 'email_open',
                data: { template: template }
            });
        }
    }
    
    trackEmailClick(leadId, template) {
        const lead = this.leads.get(leadId);
        if (lead) {
            this.leadTracker.update(leadId, {
                type: 'email_click',
                data: { template: template }
            });
        }
    }
    
    // Campaign-specific triggers
    triggerCalculatorSequence(lead) {
        this.addToEmailSequence(lead, 'calculator_abandonment');
        this.analytics.trackCampaignTrigger('calculator_sequence', lead);
    }
    
    triggerFormCompletionSequence(lead) {
        this.sendEmail(lead, 'form_completion');
        this.notifySalesTeam(lead);
        this.analytics.trackCampaignTrigger('form_completion', lead);
    }
    
    addToEmailSequence(lead, sequenceName) {
        const sequence = this.emailSequences[sequenceName];
        if (!sequence) return;
        
        sequence.emails.forEach(email => {
            setTimeout(() => {
                this.sendEmail(lead, email.template, {
                    subject: email.subject,
                    personalization: email.personalization
                });
            }, email.delay);
        });
    }
    
    notifySalesTeam(lead) {
        const notification = {
            type: 'new_qualified_lead',
            lead: lead,
            timestamp: new Date().toISOString(),
            priority: lead.score > 70 ? 'high' : 'normal'
        };
        
        // In a real implementation, this would send to CRM or sales team
        console.log('Sales notification:', notification);
        
        // Show notification in admin interface
        if (window.notifications) {
            window.notifications.info(`Novo lead qualificado: ${lead.data.name || 'Lead'} (Score: ${lead.score})`);
        }
    }
    
    // Public API methods
    captureLead(source, data) {
        return this.leadTracker.capture(source, data);
    }
    
    trackActivity(leadId, activity) {
        this.leadTracker.update(leadId, activity);
    }
    
    getLeadById(leadId) {
        return this.leads.get(leadId);
    }
    
    getLeadsBySegment(segment) {
        return Array.from(this.leads.values()).filter(lead => 
            lead.segments.includes(segment)
        );
    }
    
    getAnalytics() {
        return this.analytics.getReport();
    }
    
    generateLeadId() {
        return 'lead_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    storeLead(lead) {
        // Store in localStorage for demo purposes
        const stored = JSON.parse(localStorage.getItem('logyc_leads') || '[]');
        stored.push(lead);
        
        // Keep only last 50 leads
        if (stored.length > 50) {
            stored.splice(0, stored.length - 50);
        }
        
        localStorage.setItem('logyc_leads', JSON.stringify(stored));
    }
    
    startAutomation() {
        console.log('Marketing Automation System Started');
        
        // Start periodic cleanup and optimization
        setInterval(() => {
            this.optimizeCampaigns();
            this.cleanupOldData();
        }, 3600000); // Every hour
    }
    
    optimizeCampaigns() {
        // Analyze campaign performance and optimize
        const analytics = this.analytics.getReport();
        
        // Optimize email send times
        // Adjust scoring weights
        // Update segmentation rules
        
        console.log('Campaign optimization completed');
    }
    
    cleanupOldData() {
        // Remove old leads and interactions
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 90); // 90 days ago
        
        this.leads.forEach((lead, leadId) => {
            if (new Date(lead.created_at) < cutoffDate) {
                this.leads.delete(leadId);
            }
        });
    }
}

// Marketing Analytics Class
class MarketingAnalytics {
    constructor() {
        this.events = [];
    }
    
    trackLeadCreation(lead) {
        this.events.push({
            type: 'lead_created',
            lead_id: lead.id,
            source: lead.source,
            timestamp: new Date().toISOString()
        });
    }
    
    trackScoreChange(lead, points, activity) {
        this.events.push({
            type: 'score_change',
            lead_id: lead.id,
            points: points,
            new_score: lead.score,
            activity: activity.type,
            timestamp: new Date().toISOString()
        });
    }
    
    trackWorkflowTrigger(workflowId, lead) {
        this.events.push({
            type: 'workflow_triggered',
            workflow_id: workflowId,
            lead_id: lead.id,
            timestamp: new Date().toISOString()
        });
    }
    
    trackEmailSent(lead, template) {
        this.events.push({
            type: 'email_sent',
            lead_id: lead.id,
            template: template,
            timestamp: new Date().toISOString()
        });
    }
    
    trackCampaignTrigger(campaign, lead) {
        this.events.push({
            type: 'campaign_triggered',
            campaign: campaign,
            lead_id: lead.id,
            timestamp: new Date().toISOString()
        });
    }
    
    getReport() {
        const report = {
            total_leads: this.events.filter(e => e.type === 'lead_created').length,
            total_emails: this.events.filter(e => e.type === 'email_sent').length,
            workflows_triggered: this.events.filter(e => e.type === 'workflow_triggered').length,
            campaigns_triggered: this.events.filter(e => e.type === 'campaign_triggered').length,
            average_score: this.calculateAverageScore(),
            conversion_rate: this.calculateConversionRate(),
            top_sources: this.getTopSources(),
            engagement_metrics: this.getEngagementMetrics()
        };
        
        return report;
    }
    
    calculateAverageScore() {
        const scoreEvents = this.events.filter(e => e.type === 'score_change');
        if (scoreEvents.length === 0) return 0;
        
        const totalScore = scoreEvents.reduce((sum, event) => sum + event.new_score, 0);
        return Math.round(totalScore / scoreEvents.length);
    }
    
    calculateConversionRate() {
        const totalLeads = this.events.filter(e => e.type === 'lead_created').length;
        const conversions = this.events.filter(e => e.type === 'workflow_triggered' && e.workflow_id === 'form_completer').length;
        
        return totalLeads > 0 ? Math.round((conversions / totalLeads) * 100) : 0;
    }
    
    getTopSources() {
        const sources = {};
        this.events.filter(e => e.type === 'lead_created').forEach(event => {
            sources[event.source] = (sources[event.source] || 0) + 1;
        });
        
        return Object.entries(sources)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);
    }
    
    getEngagementMetrics() {
        return {
            email_open_rate: 70, // Simulated
            email_click_rate: 20, // Simulated
            form_completion_rate: 15, // Simulated
            calculator_usage_rate: 35 // Simulated
        };
    }
}

// Initialize Marketing Automation
document.addEventListener('DOMContentLoaded', function() {
    window.marketingAutomation = new MarketingAutomation();
    
    // Integrate with existing systems
    if (window.advancedAnalytics) {
        // Override analytics to capture leads
        const originalTrackEvent = window.advancedAnalytics.trackEvent;
        window.advancedAnalytics.trackEvent = function(eventName, data) {
            // Call original tracking
            originalTrackEvent.call(this, eventName, data);
            
            // Check for lead-worthy events
            if (eventName === 'form_submit' && data.form_id) {
                const leadId = window.marketingAutomation.captureLead('form_submission', {
                    email: data.email || '<EMAIL>',
                    name: data.name || 'Lead',
                    company: data.company || '',
                    form_id: data.form_id
                });
                
                window.marketingAutomation.trackActivity(leadId, {
                    type: 'form_completed',
                    data: data
                });
            }
            
            if (eventName === 'calculator_usage') {
                const leadId = window.marketingAutomation.captureLead('calculator', {
                    company_type: data.company_type || 'unknown',
                    revenue: data.revenue || 0,
                    employees: data.employees || 0
                });
                
                window.marketingAutomation.trackActivity(leadId, {
                    type: 'calculator_used',
                    data: data
                });
            }
        };
    }
});

// Export for global use
window.MarketingAutomation = MarketingAutomation;
