// JavaScript para a calculadora de mensalidade
document.addEventListener('DOMContentLoaded', function() {
    const companyTypes = document.querySelectorAll('.company-type');
    const calculationInputs = document.getElementById('calculationInputs');
    const calcularBtn = document.getElementById('calcular');
    const newCalculationBtn = document.getElementById('newCalculation');
    const calculationResult = document.getElementById('calculationResult');
    const faturamentoInput = document.getElementById('faturamento');

    let selectedType = null;

    // Configurar seleção de tipo de empresa
    companyTypes.forEach(type => {
        type.addEventListener('click', function() {
            // Remove seleção anterior
            companyTypes.forEach(t => t.classList.remove('selected'));

            // Adiciona seleção atual
            this.classList.add('selected');
            selectedType = this.dataset.type;

            // Mostra inputs de cálculo
            calculationInputs.style.display = 'block';
            calculationResult.style.display = 'none';

            // Limpa campos
            document.getElementById('faturamento').value = '';
            document.getElementById('funcionarios').value = '';

            // Foca no campo de faturamento
            setTimeout(() => {
                document.getElementById('faturamento').focus();
            }, 100);
        });
    });

    // Máscara para faturamento
    if (faturamentoInput) {
        faturamentoInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');

            if (value.length > 0) {
                value = (parseInt(value) / 100).toFixed(2);
                value = value.replace('.', ',');
                value = value.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
                value = 'R$ ' + value;
            }

            e.target.value = value;
        });
    }

    // Botão calcular
    if (calcularBtn) {
        calcularBtn.addEventListener('click', function() {
            if (validateCalculatorInputs()) {
                performCalculation();
            }
        });
    }

    // Botão nova simulação
    if (newCalculationBtn) {
        newCalculationBtn.addEventListener('click', function() {
            resetCalculator();
        });
    }

    // Enter para calcular
    calculationInputs.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            if (validateCalculatorInputs()) {
                performCalculation();
            }
        }
    });
});

function validateCalculatorInputs() {
    const faturamento = document.getElementById('faturamento').value;
    const funcionarios = document.getElementById('funcionarios').value;

    if (!selectedType) {
        showCalculatorError('Por favor, selecione o tipo da sua empresa.');
        return false;
    }

    if (!faturamento || faturamento.trim() === '' || faturamento === 'R$ 0,00') {
        showCalculatorError('Por favor, informe o faturamento médio mensal.');
        document.getElementById('faturamento').focus();
        return false;
    }

    const faturamentoValue = parseFloat(faturamento.replace(/[^\d,]/g, '').replace(',', '.'));
    if (isNaN(faturamentoValue) || faturamentoValue <= 0) {
        showCalculatorError('Por favor, informe um valor de faturamento válido.');
        document.getElementById('faturamento').focus();
        return false;
    }

    const funcionariosValue = parseInt(funcionarios) || 0;
    if (funcionariosValue < 0) {
        showCalculatorError('O número de funcionários não pode ser negativo.');
        document.getElementById('funcionarios').focus();
        return false;
    }

    return true;
}

function performCalculation() {
    const faturamento = document.getElementById('faturamento').value;
    const funcionarios = parseInt(document.getElementById('funcionarios').value) || 0;

    const faturamentoValue = parseFloat(faturamento.replace(/[^\d,]/g, '').replace(',', '.'));

    const resultado = calculateMensalidade(selectedType, faturamentoValue, funcionarios);

    displayResult(resultado, faturamentoValue, funcionarios);
}

function calculateMensalidade(tipo, faturamento, funcionarios) {
    let mensalidadeBase = 0;
    let custoFuncionarios = 0;
    let breakdown = '';
    let limite = '';

    switch (tipo) {
        case 'mei':
            mensalidadeBase = 75;
            custoFuncionarios = funcionarios * 40;
            limite = 'Limite: R$ 81.000,00 anuais (R$ 6.750,00 mensais)';
            breakdown = `Mensalidade MEI: R$ 75,00\nFuncionários (${funcionarios} x R$ 40,00): R$ ${custoFuncionarios.toFixed(2)}`;

            if (faturamento > 6750) {
                breakdown += '\n⚠️ Atenção: Faturamento acima do limite MEI!';
            }
            break;

        case 'servicos':
            if (faturamento <= 20000) {
                mensalidadeBase = 285;
            } else {
                mensalidadeBase = 370;
            }

            const funcionariosExtras = Math.max(0, funcionarios - 3);
            custoFuncionarios = funcionariosExtras * 25;

            breakdown = `Mensalidade base: R$ ${mensalidadeBase.toFixed(2)}\nFuncionários inclusos: até 3\nFuncionários extras (${funcionariosExtras} x R$ 25,00): R$ ${custoFuncionarios.toFixed(2)}`;
            break;

        case 'comercio':
            if (faturamento <= 25000) {
                mensalidadeBase = 245;
            } else {
                mensalidadeBase = 390;
            }

            const funcionariosExtrasComercio = Math.max(0, funcionarios - 3);
            custoFuncionarios = funcionariosExtrasComercio * 25;

            breakdown = `Mensalidade base: R$ ${mensalidadeBase.toFixed(2)}\nFuncionários inclusos: até 3\nFuncionários extras (${funcionariosExtrasComercio} x R$ 25,00): R$ ${custoFuncionarios.toFixed(2)}`;
            break;

        case 'industria':
            if (faturamento <= 35000) {
                mensalidadeBase = 300;
            } else {
                mensalidadeBase = 545;
            }

            const funcionariosExtrasIndustria = Math.max(0, funcionarios - 3);
            custoFuncionarios = funcionariosExtrasIndustria * 25;

            breakdown = `Mensalidade base: R$ ${mensalidadeBase.toFixed(2)}\nFuncionários inclusos: até 3\nFuncionários extras (${funcionariosExtrasIndustria} x R$ 25,00): R$ ${custoFuncionarios.toFixed(2)}`;
            break;

        case 'profissionais':
            if (faturamento <= 15000) {
                mensalidadeBase = 199;
            } else {
                mensalidadeBase = 234;
            }

            const funcionariosExtrasProfissionais = Math.max(0, funcionarios - 3);
            custoFuncionarios = funcionariosExtrasProfissionais * 25;

            breakdown = `Mensalidade base: R$ ${mensalidadeBase.toFixed(2)}\nFuncionários inclusos: até 3\nFuncionários extras (${funcionariosExtrasProfissionais} x R$ 25,00): R$ ${custoFuncionarios.toFixed(2)}`;
            break;
    }

    const total = mensalidadeBase + custoFuncionarios;

    return {
        tipo,
        mensalidadeBase,
        custoFuncionarios,
        total,
        breakdown,
        limite
    };
}

function displayResult(resultado, faturamento, funcionarios) {
    const tipoNomes = {
        'mei': 'CNPJ MEI',
        'servicos': 'Prestação de Serviços',
        'comercio': 'Comércio',
        'industria': 'Indústria',
        'profissionais': 'Profissionais Liberais'
    };

    // Rastreamento Analytics
    if (typeof trackCalculatorUsage === 'function') {
        trackCalculatorUsage(resultado.tipo, faturamento, funcionarios, resultado.total);
    }

    document.getElementById('resultType').textContent = tipoNomes[resultado.tipo];
    document.getElementById('resultFaturamento').textContent = formatCurrency(faturamento);
    document.getElementById('resultFuncionarios').textContent = funcionarios;
    document.getElementById('resultTotal').textContent = formatCurrency(resultado.total);

    const breakdownDiv = document.getElementById('resultBreakdown');
    breakdownDiv.innerHTML = `
        <strong>Detalhamento:</strong><br>
        ${resultado.breakdown.replace(/\n/g, '<br>')}
        ${resultado.limite ? '<br><br><strong>' + resultado.limite + '</strong>' : ''}
    `;

    // Mostra resultado e esconde inputs
    document.getElementById('calculationInputs').style.display = 'none';
    document.getElementById('calculationResult').style.display = 'block';

    // Scroll para o resultado
    document.getElementById('calculationResult').scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    });
}

function resetCalculator() {
    // Remove seleções
    document.querySelectorAll('.company-type').forEach(type => {
        type.classList.remove('selected');
    });

    selectedType = null;

    // Limpa campos
    document.getElementById('faturamento').value = '';
    document.getElementById('funcionarios').value = '';

    // Esconde seções
    document.getElementById('calculationInputs').style.display = 'none';
    document.getElementById('calculationResult').style.display = 'none';

    // Scroll para o topo da calculadora
    document.querySelector('.calculator-title').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

function formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(value);
}

function showCalculatorError(message) {
    // Remove alertas anteriores
    const existingAlert = document.querySelector('.calculator-error');
    if (existingAlert) {
        existingAlert.remove();
    }

    // Cria novo alerta
    const alertDiv = document.createElement('div');
    alertDiv.className = 'calculator-error';
    alertDiv.style.cssText = `
        background: #fd0e35;
        color: white;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
        text-align: center;
        animation: shake 0.5s ease-in-out;
    `;

    alertDiv.innerHTML = `
        <strong>Atenção:</strong> ${message}
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer; margin-top: -0.5rem;">&times;</button>
    `;

    // Insere antes dos inputs de cálculo
    const calculationInputs = document.getElementById('calculationInputs');
    calculationInputs.parentNode.insertBefore(alertDiv, calculationInputs);

    // Remove automaticamente após 5 segundos
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

// Adicionar animação de shake
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    .company-type {
        transition: all 0.3s ease;
    }

    .company-type:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .company-type.selected {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 90, 236, 0.3);
    }
`;
document.head.appendChild(style);

// Exemplo de uso da calculadora (para demonstração)
function exemploCalculadora() {
    console.log('Exemplos de cálculo:');

    // MEI
    console.log('MEI - R$ 5.000/mês, 0 funcionários:', calculateMensalidade('mei', 5000, 0));

    // Serviços
    console.log('Serviços - R$ 15.000/mês, 2 funcionários:', calculateMensalidade('servicos', 15000, 2));
    console.log('Serviços - R$ 58.900/mês, 5 funcionários:', calculateMensalidade('servicos', 58900, 5));

    // Comércio
    console.log('Comércio - R$ 20.000/mês, 3 funcionários:', calculateMensalidade('comercio', 20000, 3));

    // Indústria
    console.log('Indústria - R$ 30.000/mês, 4 funcionários:', calculateMensalidade('industria', 30000, 4));

    // Profissionais Liberais
    console.log('Profissionais - R$ 12.000/mês, 1 funcionário:', calculateMensalidade('profissionais', 12000, 1));
}

// Descomente a linha abaixo para ver exemplos no console
// exemploCalculadora();
