/* Estilos específicos para formulários */

/* Seção de formulário */
.form-section {
    padding: 120px 0 80px;
    background: var(--cinza-claro);
    min-height: 100vh;
}

.form-title {
    text-align: center;
    margin-bottom: 3rem;
}

.form-title h1 {
    background: var(--amarelo);
    color: var(--cinza-escuro);
    padding: 1rem 2rem;
    border-radius: 5px;
    font-weight: bold;
    display: inline-block;
    font-size: 1.5rem;
}

/* Formulários específicos */
.help-form,
.switch-form {
    max-width: 600px;
    margin: 0 auto;
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.form-group label {
    min-width: 180px;
    font-weight: bold;
    padding-top: 0.75rem;
}

.form-group input,
.form-group textarea {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--azul-escuro);
    box-shadow: 0 0 5px rgba(0, 90, 236, 0.3);
}

.required-label {
    font-weight: bold;
    margin-bottom: 1rem;
    display: block;
    min-width: auto;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-left: 1rem;
}

.radio-group.horizontal {
    flex-direction: row;
    gap: 2rem;
    margin-left: 0;
}

.radio-group label {
    min-width: auto;
    font-weight: normal;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-top: 0;
}

.radio-group input[type="radio"] {
    width: auto;
    margin: 0;
}

/* Campos condicionais */
.conditional {
    transition: all 0.3s ease;
}

/* Botões */
.btn-submit,
.btn-calculate {
    background: var(--verde-claro);
    color: var(--branco);
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
    margin-top: 1rem;
}

.btn-submit:hover,
.btn-calculate:hover {
    background: var(--verde-escuro);
}

.btn-secondary {
    background: var(--azul-claro);
    color: var(--branco);
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary:hover {
    background: var(--azul-escuro);
}

/* Observações */
.observacoes {
    max-width: 600px;
    margin: 2rem auto 0;
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.observacoes h3 {
    background: var(--amarelo);
    color: var(--cinza-escuro);
    padding: 0.5rem 1rem;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.observacoes ol {
    padding-left: 1rem;
}

.observacoes li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

/* Seção de conteúdo */
.content-section {
    padding: 80px 0;
    background: var(--branco);
}

.content-wrapper h2 {
    color: var(--azul-escuro);
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.content-wrapper h3 {
    color: var(--verde-escuro);
    font-size: 1.8rem;
    text-align: center;
    margin-bottom: 2rem;
}

.content-wrapper h4 {
    color: var(--azul-escuro);
    font-size: 1.3rem;
    margin: 2rem 0 1rem;
}

.content-wrapper p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    text-align: justify;
}

.service-areas {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin: 2rem 0;
}

.service-area {
    background: var(--cinza-claro);
    padding: 2rem;
    border-radius: 10px;
    border-left: 5px solid var(--verde-claro);
}

.service-area h5 {
    color: var(--azul-escuro);
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.closing-message {
    background: var(--azul-escuro);
    color: var(--branco);
    padding: 2rem;
    border-radius: 10px;
    margin-top: 3rem;
    text-align: center;
}

.closing-message p {
    margin-bottom: 1rem;
}

/* Seção de informações */
.info-section {
    padding: 80px 0;
    background: var(--cinza-claro);
}

.info-content h2 {
    color: var(--azul-escuro);
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.benefit-item {
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.benefit-item h3 {
    color: var(--azul-escuro);
    margin-bottom: 1rem;
}

.process-info h3 {
    color: var(--azul-escuro);
    font-size: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step {
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
}

.step-number {
    background: var(--verde-claro);
    color: var(--branco);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin: 0 auto 1rem;
}

.step h4 {
    color: var(--azul-escuro);
    margin-bottom: 1rem;
}

/* Calculadora */
.calculator-section {
    padding: 120px 0 80px;
    background: var(--cinza-claro);
    min-height: 100vh;
}

.calculator-title {
    text-align: center;
    margin-bottom: 3rem;
}

.calculator-title h1 {
    color: var(--azul-escuro);
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.calculator-title p {
    font-size: 1.2rem;
    color: var(--cinza-escuro);
}

.calculator-wrapper {
    max-width: 800px;
    margin: 0 auto;
    background: var(--branco);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.calculator-form {
    padding: 2rem;
}

.calculator-form h2 {
    color: var(--azul-escuro);
    text-align: center;
    margin-bottom: 2rem;
}

.company-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.company-type {
    background: var(--cinza-claro);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    border: 2px solid transparent;
}

.company-type:hover {
    background: var(--azul-claro);
    color: var(--branco);
    transform: translateY(-2px);
}

.company-type.selected {
    background: var(--azul-escuro);
    color: var(--branco);
    border-color: var(--verde-claro);
}

.company-type h3 {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.company-type p {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.regime {
    font-size: 0.8rem;
    font-style: italic;
}

.calculation-inputs {
    border-top: 2px solid var(--cinza-claro);
    padding-top: 2rem;
    margin-top: 2rem;
}

.calculation-inputs h3 {
    color: var(--azul-escuro);
    margin-bottom: 1.5rem;
}

.input-group {
    margin-bottom: 1rem;
}

.input-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.input-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

/* Resultado da calculadora */
.calculation-result {
    background: var(--verde-claro);
    color: var(--branco);
    padding: 2rem;
}

.calculation-result h3 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

.result-content {
    margin-bottom: 2rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.3);
}

.result-item.total {
    font-size: 1.2rem;
    font-weight: bold;
    border-bottom: 2px solid var(--branco);
    margin-top: 1rem;
    padding-top: 1rem;
}

.result-breakdown {
    background: rgba(255,255,255,0.1);
    padding: 1rem;
    border-radius: 5px;
    margin-top: 1rem;
    font-size: 0.9rem;
}

.result-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Informações da calculadora */
.calculator-info {
    max-width: 1000px;
    margin: 3rem auto 0;
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.calculator-info h3 {
    color: var(--azul-escuro);
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.info-item {
    background: var(--cinza-claro);
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 5px solid var(--verde-claro);
}

.info-item h4 {
    color: var(--azul-escuro);
    margin-bottom: 1rem;
}

.info-item ul {
    list-style: none;
    padding: 0;
}

.info-item li {
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.info-item li:before {
    content: "•";
    color: var(--verde-claro);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.disclaimer {
    background: var(--amarelo);
    color: var(--cinza-escuro);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
}

/* Responsividade para formulários */
@media (max-width: 768px) {
    .form-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .form-group label {
        min-width: auto;
        padding-top: 0;
    }
    
    .radio-group.horizontal {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .company-types {
        grid-template-columns: 1fr;
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .benefits-grid,
    .process-steps,
    .info-grid {
        grid-template-columns: 1fr;
    }
}
