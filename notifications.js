// Sistema de Notificações Avançado para Logyc Contabilidade
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.init();
    }
    
    init() {
        this.createContainer();
        this.setupStyles();
    }
    
    createContainer() {
        this.container = document.createElement('div');
        this.container.className = 'notification-container';
        this.container.setAttribute('aria-live', 'polite');
        this.container.setAttribute('aria-atomic', 'false');
        document.body.appendChild(this.container);
    }
    
    setupStyles() {
        const styles = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                max-width: 400px;
                pointer-events: none;
            }
            
            .notification {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                margin-bottom: 10px;
                padding: 16px;
                pointer-events: auto;
                transform: translateX(100%);
                transition: all 0.3s ease;
                border-left: 4px solid #ccc;
                position: relative;
                overflow: hidden;
            }
            
            .notification.show {
                transform: translateX(0);
            }
            
            .notification.success {
                border-left-color: #01d800;
            }
            
            .notification.error {
                border-left-color: #fd0e35;
            }
            
            .notification.warning {
                border-left-color: #ffe206;
            }
            
            .notification.info {
                border-left-color: #0593ff;
            }
            
            .notification-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            
            .notification-title {
                font-weight: bold;
                font-size: 14px;
                color: #333;
            }
            
            .notification-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .notification-close:hover {
                color: #333;
            }
            
            .notification-message {
                font-size: 13px;
                color: #666;
                line-height: 1.4;
            }
            
            .notification-actions {
                margin-top: 12px;
                display: flex;
                gap: 8px;
            }
            
            .notification-action {
                background: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                cursor: pointer;
                transition: background 0.2s;
            }
            
            .notification-action:hover {
                background: #e9e9e9;
            }
            
            .notification-action.primary {
                background: #005aec;
                color: white;
                border-color: #005aec;
            }
            
            .notification-action.primary:hover {
                background: #0047ba;
            }
            
            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(0, 0, 0, 0.1);
                transition: width linear;
            }
            
            .notification.success .notification-progress {
                background: #01d800;
            }
            
            .notification.error .notification-progress {
                background: #fd0e35;
            }
            
            .notification.warning .notification-progress {
                background: #ffe206;
            }
            
            .notification.info .notification-progress {
                background: #0593ff;
            }
            
            @media (max-width: 768px) {
                .notification-container {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                    max-width: none;
                }
                
                .notification {
                    margin-bottom: 8px;
                    padding: 12px;
                }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }
    
    show(options) {
        const notification = this.createNotification(options);
        this.notifications.push(notification);
        this.container.appendChild(notification.element);
        
        // Trigger animation
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);
        
        // Auto remove
        if (options.duration !== 0) {
            const duration = options.duration || this.getDefaultDuration(options.type);
            this.startAutoRemove(notification, duration);
        }
        
        return notification;
    }
    
    createNotification(options) {
        const {
            type = 'info',
            title,
            message,
            actions = [],
            duration,
            persistent = false
        } = options;
        
        const element = document.createElement('div');
        element.className = `notification ${type}`;
        
        const header = document.createElement('div');
        header.className = 'notification-header';
        
        const titleElement = document.createElement('div');
        titleElement.className = 'notification-title';
        titleElement.textContent = title || this.getDefaultTitle(type);
        
        const closeButton = document.createElement('button');
        closeButton.className = 'notification-close';
        closeButton.innerHTML = '×';
        closeButton.setAttribute('aria-label', 'Fechar notificação');
        
        header.appendChild(titleElement);
        header.appendChild(closeButton);
        
        const messageElement = document.createElement('div');
        messageElement.className = 'notification-message';
        messageElement.textContent = message;
        
        element.appendChild(header);
        element.appendChild(messageElement);
        
        // Add actions
        if (actions.length > 0) {
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'notification-actions';
            
            actions.forEach(action => {
                const button = document.createElement('button');
                button.className = `notification-action ${action.primary ? 'primary' : ''}`;
                button.textContent = action.text;
                button.addEventListener('click', () => {
                    if (action.handler) action.handler();
                    this.remove(notification);
                });
                actionsContainer.appendChild(button);
            });
            
            element.appendChild(actionsContainer);
        }
        
        // Add progress bar for auto-remove
        if (!persistent && duration !== 0) {
            const progress = document.createElement('div');
            progress.className = 'notification-progress';
            element.appendChild(progress);
        }
        
        const notification = {
            id: Date.now() + Math.random(),
            element,
            type,
            persistent
        };
        
        // Close button handler
        closeButton.addEventListener('click', () => {
            this.remove(notification);
        });
        
        return notification;
    }
    
    startAutoRemove(notification, duration) {
        const progressBar = notification.element.querySelector('.notification-progress');
        
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transition = `width ${duration}ms linear`;
            
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 10);
        }
        
        setTimeout(() => {
            this.remove(notification);
        }, duration);
    }
    
    remove(notification) {
        notification.element.classList.remove('show');
        
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }
    
    removeAll() {
        this.notifications.forEach(notification => {
            this.remove(notification);
        });
    }
    
    getDefaultTitle(type) {
        const titles = {
            success: 'Sucesso!',
            error: 'Erro!',
            warning: 'Atenção!',
            info: 'Informação'
        };
        return titles[type] || 'Notificação';
    }
    
    getDefaultDuration(type) {
        const durations = {
            success: 4000,
            error: 6000,
            warning: 5000,
            info: 4000
        };
        return durations[type] || 4000;
    }
    
    // Métodos de conveniência
    success(message, options = {}) {
        return this.show({
            type: 'success',
            message,
            ...options
        });
    }
    
    error(message, options = {}) {
        return this.show({
            type: 'error',
            message,
            ...options
        });
    }
    
    warning(message, options = {}) {
        return this.show({
            type: 'warning',
            message,
            ...options
        });
    }
    
    info(message, options = {}) {
        return this.show({
            type: 'info',
            message,
            ...options
        });
    }
}

// Criar instância global
window.notifications = new NotificationSystem();

// Integração com formulários existentes
document.addEventListener('DOMContentLoaded', function() {
    // Substituir alertas antigos por notificações
    const originalShowErrors = window.showErrors;
    const originalShowSuccessMessage = window.showSuccessMessage;
    
    window.showErrors = function(errors) {
        errors.forEach(error => {
            notifications.error(error, { duration: 6000 });
        });
    };
    
    window.showSuccessMessage = function(message) {
        notifications.success(message || 'Operação realizada com sucesso!', {
            actions: [
                {
                    text: 'OK',
                    primary: true,
                    handler: () => {}
                }
            ]
        });
    };
    
    // Notificações específicas para calculadora
    window.showCalculatorError = function(message) {
        notifications.warning(message, {
            title: 'Calculadora',
            duration: 5000
        });
    };
    
    // Notificação de boas-vindas
    setTimeout(() => {
        if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
            notifications.info('Bem-vindo à Logyc Contabilidade! Use nossa calculadora para descobrir o valor da mensalidade.', {
                title: 'Bem-vindo!',
                duration: 6000,
                actions: [
                    {
                        text: 'Calculadora',
                        primary: true,
                        handler: () => {
                            window.location.href = '/calculadora.html';
                        }
                    },
                    {
                        text: 'Fechar',
                        handler: () => {}
                    }
                ]
            });
        }
    }, 2000);
});

// Notificações para eventos especiais
window.addEventListener('online', () => {
    notifications.success('Conexão restaurada!', { duration: 3000 });
});

window.addEventListener('offline', () => {
    notifications.warning('Você está offline. Algumas funcionalidades podem não funcionar.', {
        persistent: true
    });
});

// Exportar para uso global
window.NotificationSystem = NotificationSystem;
