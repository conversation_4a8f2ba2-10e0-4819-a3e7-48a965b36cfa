# Site Institucional - Logyc Contabilidade

Site institucional desenvolvido para a Logyc Contabilidade, uma empresa especializada em serviços contábeis, fiscais e de gestão empresarial.

## 📋 Estrutura do Projeto

### Páginas Principais
- **index.html** - Página principal com informações da empresa e formulário de contato
- **como-podemos-ajudar.html** - Página com formulário específico e conteúdo institucional
- **troca-contabilidade.html** - Página para solicitação de troca de contabilidade
- **calculadora.html** - Calculadora de mensalidade contábil

### Arquivos de Estilo
- **styles.css** - Estilos principais do site
- **forms.css** - Estilos específicos para formulários

### Arquivos JavaScript
- **script.js** - Funcionalidades gerais do site
- **help-form.js** - JavaScript específico para o formulário "Como Podemos Ajudar"
- **switch-form.js** - JavaScript para o formulário de troca de contabilidade
- **calculator.js** - Lógica da calculadora de mensalidade

## 🎨 Paleta de Cores

| Cor | HEX | RGB | Uso |
|-----|-----|-----|-----|
| Verde Claro | #01d800 | rgba(1,216,0,255) | Botões principais, destaques |
| Verde Escuro | #217345 | rgba(33,115,69,255) | Hover states, textos secundários |
| Vermelho | #fd0e35 | rgba(253,14,53,255) | Campos obrigatórios, erros |
| Amarelo | #ffe206 | rgba(255,226,6,255) | Títulos de formulários, observações |
| Azul Claro | #0593ff | rgba(5,147,255,255) | Botões secundários |
| Azul Escuro | #005aec | rgba(0,90,236,255) | Cor principal, títulos |

## 🚀 Funcionalidades

### Formulários
1. **Formulário de Contato** (página principal)
   - Validação de campos obrigatórios
   - Validação condicional para campo "Outros"
   - Formatação automática de dados

2. **Formulário "Como Podemos Ajudar"**
   - Mesma estrutura do formulário de contato
   - Página dedicada com conteúdo institucional

3. **Formulário "Troca de Contabilidade"**
   - Campos condicionais (CNPJ e funcionários)
   - Máscara automática para CNPJ
   - Validação de CNPJ
   - Formatação monetária

### Calculadora de Mensalidade
- Seleção de tipo de empresa (MEI, Serviços, Comércio, Indústria, Profissionais Liberais)
- Cálculo automático baseado em faturamento e número de funcionários
- Detalhamento dos custos
- Interface interativa

### Recursos Técnicos
- Design responsivo
- Smooth scrolling
- Validações em tempo real
- Máscaras de entrada
- Animações CSS
- Alertas de sucesso/erro

## 📱 Responsividade

O site é totalmente responsivo e se adapta a diferentes tamanhos de tela:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (até 767px)

## 🔧 Tecnologias Utilizadas

- **HTML5** - Estrutura semântica
- **CSS3** - Estilização e animações
- **JavaScript (Vanilla)** - Funcionalidades interativas
- **CSS Grid e Flexbox** - Layout responsivo

## 📋 Validações Implementadas

### Campos Obrigatórios
- Todos os campos marcados com asterisco (*) são obrigatórios
- Validação visual com bordas vermelhas
- Mensagens de erro específicas

### Validações Específicas
- **Email**: Formato válido de email
- **WhatsApp**: Mínimo 10 dígitos, máximo 15
- **CNPJ**: Validação completa com dígitos verificadores
- **Faturamento**: Valores numéricos positivos
- **Campos condicionais**: Aparecem/desaparecem conforme seleção

## 🎯 Regras de Negócio

### Calculadora de Mensalidade

#### MEI (Microempreendedor Individual)
- Limite: R$ 81.000,00 anuais
- Mensalidade: R$ 75,00
- Funcionários: +R$ 40,00 por funcionário

#### Prestação de Serviços
- Até R$ 20.000/mês: R$ 285,00
- Acima R$ 35.000/mês: R$ 370,00
- Até 3 funcionários inclusos, +R$ 25,00 por funcionário adicional

#### Comércio
- Até R$ 25.000/mês: R$ 245,00
- Acima R$ 45.000/mês: R$ 390,00
- Até 3 funcionários inclusos, +R$ 25,00 por funcionário adicional

#### Indústria
- Até R$ 35.000/mês: R$ 300,00
- Acima R$ 50.000/mês: R$ 545,00
- Até 3 funcionários inclusos, +R$ 25,00 por funcionário adicional

#### Profissionais Liberais
- Até R$ 15.000/mês: R$ 199,00
- Acima R$ 25.000/mês: R$ 234,00
- Até 3 funcionários inclusos, +R$ 25,00 por funcionário adicional

## 🔄 Integração Futura

O código está preparado para integração com:
- WhatsApp Business API
- Sistemas de CRM
- Email marketing
- Analytics (Google Analytics, Facebook Pixel)

## 📞 Contato

**Logyc Contabilidade**
- Localização: Curitiba - PR
- Atendimento: Todo Brasil
- Site: [Em desenvolvimento]

## 📝 Observações

- Todos os formulários geram mensagens formatadas para WhatsApp
- Os dados são validados no frontend (implementar validação backend)
- As mensagens de sucesso/erro são exibidas com animações
- O site está otimizado para SEO básico
- Código limpo e bem documentado para facilitar manutenção

## 🚀 Como Usar

1. Abra o arquivo `index.html` em um navegador
2. Navegue pelas diferentes seções e páginas
3. Teste os formulários e a calculadora
4. Verifique a responsividade em diferentes dispositivos

## 🔧 Manutenção

Para adicionar novos serviços ou modificar preços da calculadora, edite o arquivo `calculator.js` na função `calculateMensalidade()`.

Para modificar cores, edite as variáveis CSS no arquivo `styles.css` na seção `:root`.

---

*Desenvolvido para Logyc Contabilidade - Seu Sucesso é Nossa Prioridade*
