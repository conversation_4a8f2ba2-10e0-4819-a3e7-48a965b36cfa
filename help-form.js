// JavaScript específico para o formulário "Como Podemos Ajudar"
document.addEventListener('DOMContentLoaded', function() {
    const helpForm = document.getElementById('helpForm');

    if (helpForm) {
        // Validação em tempo real para o campo "Outros"
        const radioButtons = document.querySelectorAll('input[name="opcao"]');
        const mensagemField = document.getElementById('mensagem');

        radioButtons.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'outros') {
                    mensagemField.setAttribute('required', 'required');
                    mensagemField.style.borderColor = '#fd0e35';
                    mensagemField.placeholder = 'Este campo é obrigatório quando "Outros" é selecionado';

                    // Adiciona indicador visual
                    const label = document.querySelector('label[for="mensagem"]');
                    if (label && !label.textContent.includes('*')) {
                        label.innerHTML = '*Escreva Como podemos Ajudar: <span style="color: #fd0e35;">(Obrigatório)</span>';
                    }
                } else {
                    mensagemField.removeAttribute('required');
                    mensagemField.style.borderColor = '#ddd';
                    mensagemField.placeholder = 'Escreva Como podemos Ajudar:';

                    // Remove indicador visual
                    const label = document.querySelector('label[for="mensagem"]');
                    if (label) {
                        label.innerHTML = 'Escreva Como podemos Ajudar:';
                    }
                }
            });
        });

        // Submissão do formulário
        helpForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (validateHelpForm()) {
                handleHelpFormSubmission();
            }
        });
    }
});

function validateHelpForm() {
    const form = document.getElementById('helpForm');
    const formData = new FormData(form);
    let isValid = true;
    let errors = [];

    // Limpar estilos de erro anteriores
    clearErrorStyles();

    // Validar campos obrigatórios
    const requiredFields = ['nome', 'whatsapp', 'email', 'cidade', 'opcao'];

    requiredFields.forEach(field => {
        const value = formData.get(field);
        if (!value || value.trim() === '') {
            isValid = false;
            errors.push(`O campo ${getFieldLabel(field)} é obrigatório.`);
            highlightErrorField(field);
        }
    });

    // Validação específica para "Outros"
    const opcaoSelecionada = formData.get('opcao');
    const mensagem = formData.get('mensagem');

    if (opcaoSelecionada === 'outros' && (!mensagem || mensagem.trim() === '')) {
        isValid = false;
        errors.push('Quando "Outros" é selecionado, o campo de mensagem é obrigatório.');
        highlightErrorField('mensagem');
    }

    // Validação de email
    const email = formData.get('email');
    if (email && !isValidEmail(email)) {
        isValid = false;
        errors.push('Por favor, insira um email válido.');
        highlightErrorField('email');
    }

    // Validação de WhatsApp
    const whatsapp = formData.get('whatsapp');
    if (whatsapp && !isValidWhatsApp(whatsapp)) {
        isValid = false;
        errors.push('Por favor, insira um número de WhatsApp válido (apenas números, mínimo 10 dígitos).');
        highlightErrorField('whatsapp');
    }

    if (!isValid) {
        showErrors(errors);
        // Scroll para o primeiro erro
        const firstErrorField = document.querySelector('.error-field');
        if (firstErrorField) {
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    return isValid;
}

function handleHelpFormSubmission() {
    const form = document.getElementById('helpForm');
    const formData = new FormData(form);

    // Coleta os dados do formulário
    const dados = {
        nome: formData.get('nome'),
        whatsapp: formData.get('whatsapp'),
        email: formData.get('email'),
        cidade: formData.get('cidade'),
        opcao: formData.get('opcao'),
        mensagem: formData.get('mensagem') || ''
    };

    // Rastreamento Analytics
    if (typeof trackFormSubmission === 'function') {
        trackFormSubmission('help_form', {
            service_type: dados.opcao,
            city: dados.cidade,
            has_message: dados.mensagem.length > 0
        });
    }

    // Cria mensagem para WhatsApp
    const mensagemWhatsApp = criarMensagemWhatsAppHelp(dados);

    // Mostra sucesso
    showSuccessMessage('Sua solicitação foi enviada com sucesso! Entraremos em contato em breve para entender melhor como podemos ajudar.');

    // Limpa o formulário
    form.reset();

    // Remove validações condicionais
    const mensagemField = document.getElementById('mensagem');
    mensagemField.removeAttribute('required');
    mensagemField.style.borderColor = '#ddd';
    mensagemField.placeholder = 'Escreva Como podemos Ajudar:';

    console.log('Dados do formulário "Como Podemos Ajudar":', dados);
    console.log('Mensagem WhatsApp:', mensagemWhatsApp);

    // Abrir WhatsApp
    window.open(`https://wa.me/5541987427111?text=${encodeURIComponent(mensagemWhatsApp)}`, '_blank');
}

function criarMensagemWhatsAppHelp(dados) {
    const opcaoTexto = {
        'abertura-empresa': 'Abertura de Empresa',
        'baixa-cnpj': 'Baixa de CNPJ',
        'alteracao-contrato': 'Alteração de Contrato',
        'departamento-pessoal': 'Departamento Pessoal',
        'departamento-fiscal': 'Departamento Fiscal',
        'consultoria': 'Consultoria',
        'outros': 'Outros'
    };

    return `*Como Podemos Te Ajudar - Logyc Contabilidade*

*Nome:* ${dados.nome}
*WhatsApp:* ${dados.whatsapp}
*Email:* ${dados.email}
*Cidade:* ${dados.cidade}
*Serviço Solicitado:* ${opcaoTexto[dados.opcao] || dados.opcao}
*Mensagem:* ${dados.mensagem || 'Não informado'}

_Enviado através da página "Como Podemos Ajudar" do site da Logyc Contabilidade_`;
}

function clearErrorStyles() {
    // Remove classes de erro de todos os campos
    const errorFields = document.querySelectorAll('.error-field');
    errorFields.forEach(field => {
        field.classList.remove('error-field');
        field.style.borderColor = '#ddd';
        field.style.boxShadow = 'none';
    });
}

function highlightErrorField(fieldName) {
    const field = document.getElementById(fieldName) || document.querySelector(`input[name="${fieldName}"]`);
    if (field) {
        field.classList.add('error-field');
        field.style.borderColor = '#fd0e35';
        field.style.boxShadow = '0 0 5px rgba(253, 14, 53, 0.3)';

        // Para radio buttons, destacar o grupo todo
        if (field.type === 'radio') {
            const radioGroup = document.querySelector('.radio-group');
            if (radioGroup) {
                radioGroup.style.borderLeft = '3px solid #fd0e35';
                radioGroup.style.paddingLeft = '1rem';
            }
        }

        // Remover destaque após correção
        field.addEventListener('input', function() {
            this.classList.remove('error-field');
            this.style.borderColor = '#ddd';
            this.style.boxShadow = 'none';
        }, { once: true });

        // Para radio buttons
        if (field.type === 'radio') {
            const allRadios = document.querySelectorAll(`input[name="${fieldName}"]`);
            allRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    const radioGroup = document.querySelector('.radio-group');
                    if (radioGroup) {
                        radioGroup.style.borderLeft = 'none';
                        radioGroup.style.paddingLeft = '1rem';
                    }
                }, { once: true });
            });
        }
    }
}

function getFieldLabel(fieldName) {
    const labels = {
        'nome': 'Nome',
        'whatsapp': 'Número WhatsApp',
        'email': 'Email',
        'cidade': 'Cidade',
        'opcao': 'Seleção de Opção'
    };
    return labels[fieldName] || fieldName;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidWhatsApp(whatsapp) {
    // Remove caracteres não numéricos
    const cleanNumber = whatsapp.replace(/\D/g, '');
    // Verifica se tem pelo menos 10 dígitos (formato brasileiro)
    return cleanNumber.length >= 10 && cleanNumber.length <= 15;
}

function showErrors(errors) {
    // Remove alertas anteriores
    const existingAlert = document.querySelector('.error-alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // Cria novo alerta
    const alertDiv = document.createElement('div');
    alertDiv.className = 'error-alert';
    alertDiv.style.cssText = `
        background: #fd0e35;
        color: white;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        position: fixed;
        top: 100px;
        right: 20px;
        max-width: 400px;
        z-index: 1001;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease;
    `;

    alertDiv.innerHTML = `
        <strong>Erro no formulário:</strong><br>
        ${errors.join('<br>')}
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer; margin-top: -0.5rem;">&times;</button>
    `;

    document.body.appendChild(alertDiv);

    // Remove automaticamente após 7 segundos
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => alertDiv.remove(), 300);
        }
    }, 7000);
}

function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-alert';
    successDiv.style.cssText = `
        background: #01d800;
        color: white;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        position: fixed;
        top: 100px;
        right: 20px;
        max-width: 400px;
        z-index: 1001;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease;
    `;

    successDiv.innerHTML = `
        <strong>Sucesso!</strong><br>
        ${message}
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer; margin-top: -0.5rem;">&times;</button>
    `;

    document.body.appendChild(successDiv);

    setTimeout(() => {
        if (successDiv.parentElement) {
            successDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => successDiv.remove(), 300);
        }
    }, 6000);
}

// Adicionar animações CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
