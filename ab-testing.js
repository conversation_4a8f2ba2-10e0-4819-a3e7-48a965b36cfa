// Sistema de A/B Testing - Logyc Contabilidade
class ABTestingSystem {
    constructor() {
        this.tests = this.getActiveTests();
        this.userVariants = {};
        this.init();
    }
    
    init() {
        this.loadUserVariants();
        this.assignVariants();
        this.applyVariants();
        this.setupTracking();
    }
    
    getActiveTests() {
        return {
            'hero_cta_test': {
                name: 'Hero CTA Button Test',
                description: 'Testing different CTA button texts in hero section',
                status: 'active',
                traffic_allocation: 100, // 100% of users
                variants: {
                    'control': {
                        weight: 50,
                        changes: {
                            '.btn-troca': {
                                text: 'Trocar de Contabilidade'
                            }
                        }
                    },
                    'variant_a': {
                        weight: 50,
                        changes: {
                            '.btn-troca': {
                                text: 'Quero Trocar Agora',
                                style: {
                                    background: '#01d800',
                                    animation: 'pulse 2s infinite'
                                }
                            }
                        }
                    }
                }
            },
            
            'calculator_position_test': {
                name: '<PERSON><PERSON>tor But<PERSON> Position',
                description: 'Testing calculator button position in navigation',
                status: 'active',
                traffic_allocation: 50, // 50% of users
                variants: {
                    'control': {
                        weight: 50,
                        changes: {}
                    },
                    'variant_b': {
                        weight: 50,
                        changes: {
                            '.nav': {
                                append: '<li><a href="calculadora.html" class="nav-calculator-highlight">📊 Calcular Mensalidade</a></li>'
                            }
                        }
                    }
                }
            },
            
            'whatsapp_message_test': {
                name: 'WhatsApp Message Variants',
                description: 'Testing different WhatsApp message approaches',
                status: 'active',
                traffic_allocation: 100,
                variants: {
                    'control': {
                        weight: 33,
                        changes: {
                            'whatsapp_message': 'Olá! Gostaria de saber mais sobre os serviços da Logyc Contabilidade.'
                        }
                    },
                    'variant_c': {
                        weight: 33,
                        changes: {
                            'whatsapp_message': 'Oi! Vi o site da Logyc e quero uma proposta personalizada para minha empresa.'
                        }
                    },
                    'variant_d': {
                        weight: 34,
                        changes: {
                            'whatsapp_message': 'Olá! Preciso de uma contabilidade confiável. Podem me ajudar?'
                        }
                    }
                }
            },
            
            'testimonials_display_test': {
                name: 'Testimonials Display Format',
                description: 'Testing different ways to display testimonials',
                status: 'active',
                traffic_allocation: 75,
                variants: {
                    'control': {
                        weight: 50,
                        changes: {}
                    },
                    'variant_e': {
                        weight: 50,
                        changes: {
                            '.testimonials-section': {
                                style: {
                                    background: 'linear-gradient(135deg, #005aec, #0593ff)',
                                    color: 'white'
                                }
                            },
                            '.testimonial-card': {
                                style: {
                                    background: 'rgba(255,255,255,0.1)',
                                    backdropFilter: 'blur(10px)',
                                    border: '1px solid rgba(255,255,255,0.2)'
                                }
                            }
                        }
                    }
                }
            }
        };
    }
    
    loadUserVariants() {
        const stored = localStorage.getItem('logyc_ab_variants');
        if (stored) {
            this.userVariants = JSON.parse(stored);
        }
    }
    
    saveUserVariants() {
        localStorage.setItem('logyc_ab_variants', JSON.stringify(this.userVariants));
    }
    
    assignVariants() {
        Object.keys(this.tests).forEach(testId => {
            const test = this.tests[testId];
            
            // Skip if user already has variant for this test
            if (this.userVariants[testId]) {
                return;
            }
            
            // Check traffic allocation
            if (Math.random() * 100 > test.traffic_allocation) {
                this.userVariants[testId] = 'excluded';
                return;
            }
            
            // Assign variant based on weights
            const variants = Object.keys(test.variants);
            const weights = variants.map(v => test.variants[v].weight);
            const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
            
            let random = Math.random() * totalWeight;
            let selectedVariant = variants[0];
            
            for (let i = 0; i < variants.length; i++) {
                if (random < weights[i]) {
                    selectedVariant = variants[i];
                    break;
                }
                random -= weights[i];
            }
            
            this.userVariants[testId] = selectedVariant;
            
            // Track assignment
            this.trackEvent('ab_test_assignment', {
                test_id: testId,
                test_name: test.name,
                variant: selectedVariant
            });
        });
        
        this.saveUserVariants();
    }
    
    applyVariants() {
        Object.keys(this.tests).forEach(testId => {
            const test = this.tests[testId];
            const userVariant = this.userVariants[testId];
            
            if (!userVariant || userVariant === 'excluded' || userVariant === 'control') {
                return;
            }
            
            const variant = test.variants[userVariant];
            if (!variant || !variant.changes) {
                return;
            }
            
            this.applyChanges(variant.changes, testId, userVariant);
        });
    }
    
    applyChanges(changes, testId, variantName) {
        Object.keys(changes).forEach(selector => {
            const change = changes[selector];
            
            // Special handling for WhatsApp messages
            if (selector === 'whatsapp_message') {
                this.updateWhatsAppMessages(change);
                return;
            }
            
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                // Apply text changes
                if (change.text) {
                    element.textContent = change.text;
                }
                
                // Apply style changes
                if (change.style) {
                    Object.keys(change.style).forEach(property => {
                        element.style[property] = change.style[property];
                    });
                }
                
                // Apply HTML changes
                if (change.html) {
                    element.innerHTML = change.html;
                }
                
                // Append content
                if (change.append) {
                    element.insertAdjacentHTML('beforeend', change.append);
                }
                
                // Prepend content
                if (change.prepend) {
                    element.insertAdjacentHTML('afterbegin', change.prepend);
                }
                
                // Add classes
                if (change.addClass) {
                    element.classList.add(change.addClass);
                }
                
                // Remove classes
                if (change.removeClass) {
                    element.classList.remove(change.removeClass);
                }
                
                // Add attributes
                if (change.attributes) {
                    Object.keys(change.attributes).forEach(attr => {
                        element.setAttribute(attr, change.attributes[attr]);
                    });
                }
                
                // Mark element as modified by A/B test
                element.setAttribute('data-ab-test', testId);
                element.setAttribute('data-ab-variant', variantName);
            });
        });
    }
    
    updateWhatsAppMessages(newMessage) {
        // Update WhatsApp button links
        document.querySelectorAll('a[href*="wa.me"]').forEach(link => {
            const url = new URL(link.href);
            url.searchParams.set('text', newMessage);
            link.href = url.toString();
        });
        
        // Update config if available
        if (window.LogycConfig?.whatsapp?.messages) {
            window.LogycConfig.whatsapp.messages.default = newMessage;
        }
    }
    
    setupTracking() {
        // Track interactions with A/B tested elements
        document.addEventListener('click', (e) => {
            const abTest = e.target.getAttribute('data-ab-test');
            const abVariant = e.target.getAttribute('data-ab-variant');
            
            if (abTest && abVariant) {
                this.trackEvent('ab_test_interaction', {
                    test_id: abTest,
                    variant: abVariant,
                    element: e.target.tagName,
                    element_text: e.target.textContent?.substring(0, 50)
                });
            }
        });
        
        // Track conversions for A/B tested elements
        this.setupConversionTracking();
    }
    
    setupConversionTracking() {
        // Track form submissions
        document.addEventListener('submit', (e) => {
            this.trackConversionsForActiveTests('form_submit');
        });
        
        // Track WhatsApp clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('a[href*="wa.me"]')) {
                this.trackConversionsForActiveTests('whatsapp_click');
            }
        });
        
        // Track calculator usage
        if (window.location.pathname.includes('calculadora')) {
            const calcButton = document.getElementById('calcular');
            if (calcButton) {
                calcButton.addEventListener('click', () => {
                    this.trackConversionsForActiveTests('calculator_usage');
                });
            }
        }
    }
    
    trackConversionsForActiveTests(conversionType) {
        Object.keys(this.userVariants).forEach(testId => {
            const variant = this.userVariants[testId];
            if (variant && variant !== 'excluded') {
                this.trackEvent('ab_test_conversion', {
                    test_id: testId,
                    variant: variant,
                    conversion_type: conversionType,
                    test_name: this.tests[testId]?.name
                });
            }
        });
    }
    
    trackEvent(eventName, data) {
        // Use existing analytics system if available
        if (window.advancedAnalytics) {
            window.advancedAnalytics.trackEvent(eventName, data);
        } else if (window.trackEvent) {
            window.trackEvent(eventName, data);
        } else {
            console.log('A/B Test Event:', eventName, data);
        }
    }
    
    // Public methods for manual conversion tracking
    trackConversion(testId, conversionType, value = 1) {
        const variant = this.userVariants[testId];
        if (variant && variant !== 'excluded') {
            this.trackEvent('ab_test_manual_conversion', {
                test_id: testId,
                variant: variant,
                conversion_type: conversionType,
                conversion_value: value
            });
        }
    }
    
    // Get user's variant for a specific test
    getVariant(testId) {
        return this.userVariants[testId] || null;
    }
    
    // Check if user is in a specific variant
    isVariant(testId, variantName) {
        return this.userVariants[testId] === variantName;
    }
    
    // Force user into a specific variant (for testing)
    forceVariant(testId, variantName) {
        if (this.tests[testId] && this.tests[testId].variants[variantName]) {
            this.userVariants[testId] = variantName;
            this.saveUserVariants();
            
            // Reapply changes
            const variant = this.tests[testId].variants[variantName];
            if (variant.changes) {
                this.applyChanges(variant.changes, testId, variantName);
            }
            
            this.trackEvent('ab_test_force_variant', {
                test_id: testId,
                variant: variantName
            });
        }
    }
    
    // Get test results summary
    getTestResults() {
        const results = {};
        
        Object.keys(this.tests).forEach(testId => {
            const test = this.tests[testId];
            const userVariant = this.userVariants[testId];
            
            results[testId] = {
                test_name: test.name,
                user_variant: userVariant,
                test_status: test.status,
                description: test.description
            };
        });
        
        return results;
    }
    
    // Reset all A/B tests (clear user assignments)
    resetTests() {
        localStorage.removeItem('logyc_ab_variants');
        this.userVariants = {};
        
        this.trackEvent('ab_test_reset', {
            reset_timestamp: new Date().toISOString()
        });
        
        // Reload page to apply new assignments
        window.location.reload();
    }
}

// CSS for A/B test specific styles
const abTestStyles = `
    .nav-calculator-highlight {
        background: linear-gradient(45deg, #01d800, #217345) !important;
        color: white !important;
        padding: 8px 16px !important;
        border-radius: 20px !important;
        font-weight: bold !important;
        animation: glow 2s ease-in-out infinite alternate !important;
    }
    
    @keyframes glow {
        from { box-shadow: 0 0 5px #01d800; }
        to { box-shadow: 0 0 20px #01d800, 0 0 30px #01d800; }
    }
    
    [data-ab-test] {
        position: relative;
    }
    
    /* Debug mode - show A/B test indicators */
    .ab-debug [data-ab-test]::after {
        content: attr(data-ab-variant);
        position: absolute;
        top: -20px;
        left: 0;
        background: #ff0000;
        color: white;
        font-size: 10px;
        padding: 2px 4px;
        border-radius: 2px;
        z-index: 9999;
        pointer-events: none;
    }
`;

// Add A/B test styles
const abStyleSheet = document.createElement('style');
abStyleSheet.textContent = abTestStyles;
document.head.appendChild(abStyleSheet);

// Initialize A/B testing system
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure other systems are loaded
    setTimeout(() => {
        window.abTesting = new ABTestingSystem();
        
        // Debug mode activation (add ?ab_debug=1 to URL)
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('ab_debug') === '1') {
            document.body.classList.add('ab-debug');
            console.log('A/B Testing Debug Mode Enabled');
            console.log('Active Tests:', window.abTesting.getTestResults());
        }
        
        // Force variant mode (add ?ab_force=test_id:variant to URL)
        const forceParam = urlParams.get('ab_force');
        if (forceParam) {
            const [testId, variant] = forceParam.split(':');
            if (testId && variant) {
                window.abTesting.forceVariant(testId, variant);
                console.log(`Forced variant ${variant} for test ${testId}`);
            }
        }
    }, 500);
});

// Export for global use
window.ABTestingSystem = ABTestingSystem;
