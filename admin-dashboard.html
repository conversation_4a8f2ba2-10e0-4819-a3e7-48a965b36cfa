<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Administrativo - Logyc Contabilidade</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/png" href="favicon.png">
    <style>
        .admin-dashboard {
            min-height: 100vh;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .admin-header {
            background: #005aec;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .admin-nav {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .admin-nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            gap: 2rem;
        }
        
        .admin-nav a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #005aec;
            color: white;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e1e5e9;
        }
        
        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #005aec;
            margin: 0.5rem 0;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .metric-change {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .metric-change.positive {
            background: #d4edda;
            color: #155724;
        }
        
        .metric-change.negative {
            background: #f8d7da;
            color: #721c24;
        }
        
        .chart-container {
            height: 300px;
            margin-top: 1rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .admin-table th,
        .admin-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .admin-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-hot {
            background: #fd0e35;
            color: white;
        }
        
        .status-warm {
            background: #ffe206;
            color: #333;
        }
        
        .status-cold {
            background: #6c757d;
            color: white;
        }
        
        .btn-admin {
            background: #005aec;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }
        
        .btn-admin:hover {
            background: #0047ba;
        }
        
        .btn-admin.secondary {
            background: #6c757d;
        }
        
        .btn-admin.secondary:hover {
            background: #545b62;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .tab-container {
            margin-bottom: 2rem;
        }
        
        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #e1e5e9;
            margin-bottom: 1rem;
        }
        
        .tab-button {
            background: none;
            border: none;
            padding: 1rem 1.5rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            border-bottom-color: #005aec;
            color: #005aec;
            font-weight: 600;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .admin-content {
                padding: 1rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .admin-nav ul {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body class="admin-dashboard">
    <header class="admin-header">
        <h1>🚀 Dashboard Administrativo - Logyc Contabilidade</h1>
    </header>
    
    <nav class="admin-nav">
        <ul>
            <li><a href="#overview" class="nav-link active">Visão Geral</a></li>
            <li><a href="#analytics" class="nav-link">Analytics</a></li>
            <li><a href="#leads" class="nav-link">Leads</a></li>
            <li><a href="#campaigns" class="nav-link">Campanhas</a></li>
            <li><a href="#ab-tests" class="nav-link">A/B Tests</a></li>
            <li><a href="#settings" class="nav-link">Configurações</a></li>
        </ul>
    </nav>
    
    <main class="admin-content">
        <!-- Overview Section -->
        <section id="overview" class="tab-content active">
            <div class="alert alert-info">
                <strong>🎉 Sistema Funcionando Perfeitamente!</strong> Todos os sistemas avançados estão operacionais e coletando dados em tempo real.
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Visitantes Hoje</h3>
                    </div>
                    <div class="metric-value" id="visitorsToday">-</div>
                    <div class="metric-label">Visitantes únicos</div>
                    <div class="metric-change positive" id="visitorsChange">+12% vs ontem</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Leads Gerados</h3>
                    </div>
                    <div class="metric-value" id="leadsToday">-</div>
                    <div class="metric-label">Novos leads hoje</div>
                    <div class="metric-change positive" id="leadsChange">+25% vs ontem</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Taxa de Conversão</h3>
                    </div>
                    <div class="metric-value" id="conversionRate">-</div>
                    <div class="metric-label">Visitantes → Leads</div>
                    <div class="metric-change positive" id="conversionChange">+8% vs ontem</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Calculadora</h3>
                    </div>
                    <div class="metric-value" id="calculatorUsage">-</div>
                    <div class="metric-label">Usos hoje</div>
                    <div class="metric-change positive" id="calculatorChange">+18% vs ontem</div>
                </div>
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Leads Recentes</h3>
                    </div>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Nome</th>
                                    <th>Fonte</th>
                                    <th>Score</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="recentLeads">
                                <tr>
                                    <td colspan="5" class="loading">Carregando dados...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Tráfego em Tempo Real</h3>
                    </div>
                    <div class="chart-container" id="realTimeChart">
                        <div class="loading">Carregando gráfico...</div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Analytics Section -->
        <section id="analytics" class="tab-content">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Funil de Conversão</h3>
                    </div>
                    <div id="conversionFunnel">
                        <div class="loading">Carregando funil...</div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Heatmap de Cliques</h3>
                    </div>
                    <div id="heatmapData">
                        <div class="loading">Carregando heatmap...</div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Leads Section -->
        <section id="leads" class="tab-content">
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Gestão de Leads</h3>
                    <button class="btn-admin" onclick="exportLeads()">Exportar CSV</button>
                </div>
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nome</th>
                                <th>Email</th>
                                <th>Empresa</th>
                                <th>Fonte</th>
                                <th>Score</th>
                                <th>Estágio</th>
                                <th>Criado</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="allLeads">
                            <tr>
                                <td colspan="9" class="loading">Carregando leads...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
        
        <!-- Campaigns Section -->
        <section id="campaigns" class="tab-content">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Campanhas Ativas</h3>
                    </div>
                    <div id="activeCampaigns">
                        <div class="loading">Carregando campanhas...</div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Performance de Email</h3>
                    </div>
                    <div id="emailPerformance">
                        <div class="loading">Carregando métricas...</div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- A/B Tests Section -->
        <section id="ab-tests" class="tab-content">
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Testes A/B Ativos</h3>
                    <button class="btn-admin" onclick="createNewTest()">Novo Teste</button>
                </div>
                <div id="abTestResults">
                    <div class="loading">Carregando testes...</div>
                </div>
            </div>
        </section>
        
        <!-- Settings Section -->
        <section id="settings" class="tab-content">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Configurações do Sistema</h3>
                    </div>
                    <div id="systemSettings">
                        <div class="loading">Carregando configurações...</div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Backup e Exportação</h3>
                    </div>
                    <div>
                        <button class="btn-admin" onclick="exportAllData()">Exportar Todos os Dados</button>
                        <button class="btn-admin secondary" onclick="clearAllData()">Limpar Dados</button>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <script src="config.js"></script>
    <script src="advanced-analytics.js"></script>
    <script src="ab-testing.js"></script>
    <script src="marketing-automation.js"></script>
    <script src="ai-assistant.js"></script>
    <script src="admin-dashboard.js"></script>
</body>
</html>
