// Configurações Centralizadas - Logyc Contabilidade
window.LogycConfig = {
    // Informações da empresa
    company: {
        name: 'Logyc Contabilidade',
        slogan: 'Seu Sucesso é Nossa Prioridade',
        phone: '+55 41 98742-7111',
        phoneFormatted: '+55 (41) 98742-7111',
        email: '<EMAIL>',
        website: 'https://logyccontabilidade.com.br',
        address: {
            city: 'Curitiba',
            state: 'PR',
            country: 'Brasil'
        },
        coverage: 'Atendemos todo o Brasil',
        socialMedia: {
            facebook: 'https://www.facebook.com/logyccontabilidade',
            instagram: 'https://www.instagram.com/logyccontabilidade',
            linkedin: 'https://www.linkedin.com/company/logyccontabilidade'
        }
    },
    
    // WhatsApp
    whatsapp: {
        number: '*************',
        baseUrl: 'https://wa.me/',
        messages: {
            default: 'O<PERSON><PERSON>! Gostaria de saber mais sobre os serviços da Logyc Contabilidade.',
            calculator: 'Ol<PERSON>! Usei a calculadora no site e gostaria de saber mais sobre os serviços da Logyc Contabilidade.',
            switchAccounting: 'Olá! Gostaria de trocar minha contabilidade para a Logyc Contabilidade.',
            help: 'Olá! Preciso de ajuda com serviços contábeis.',
            contact: 'Olá! Entrei em contato através do site da Logyc Contabilidade.'
        }
    },
    
    // Cores da marca
    colors: {
        primary: '#005aec',
        secondary: '#0593ff',
        success: '#01d800',
        successDark: '#217345',
        warning: '#ffe206',
        error: '#fd0e35',
        white: '#ffffff',
        lightGray: '#f8f9fa',
        darkGray: '#333333'
    },
    
    // Configurações da calculadora
    calculator: {
        types: {
            mei: {
                name: 'CNPJ MEI',
                description: 'Microempreendedor Individual',
                regime: 'SIMEI / MEI',
                baseFee: 75,
                employeeFee: 40,
                revenueLimit: 81000, // anual
                revenueLimitMonthly: 6750,
                includedEmployees: 0
            },
            servicos: {
                name: 'Prestação de Serviços',
                description: 'Empresas de Serviços',
                regime: 'Simples Nacional',
                baseFee: {
                    low: { limit: 20000, fee: 285 },
                    high: { limit: Infinity, fee: 370 }
                },
                employeeFee: 25,
                includedEmployees: 3
            },
            comercio: {
                name: 'Comércio',
                description: 'Comércio Varejista e Atacadista',
                regime: 'Simples Nacional',
                baseFee: {
                    low: { limit: 25000, fee: 245 },
                    high: { limit: Infinity, fee: 390 }
                },
                employeeFee: 25,
                includedEmployees: 3
            },
            industria: {
                name: 'Indústria',
                description: 'Indústria e Construção Civil',
                regime: 'Simples Nacional',
                baseFee: {
                    low: { limit: 35000, fee: 300 },
                    high: { limit: Infinity, fee: 545 }
                },
                employeeFee: 25,
                includedEmployees: 3
            },
            profissionais: {
                name: 'Profissionais Liberais',
                description: 'Desenvolvedores, Advogados, Psicólogos, etc.',
                regime: 'Simples Nacional',
                baseFee: {
                    low: { limit: 15000, fee: 199 },
                    high: { limit: Infinity, fee: 234 }
                },
                employeeFee: 25,
                includedEmployees: 3
            }
        }
    },
    
    // Configurações de formulários
    forms: {
        validation: {
            minNameLength: 2,
            minCityLength: 2,
            minPhoneLength: 10,
            maxPhoneLength: 15,
            minMessageLength: 10,
            cnpjLength: 14
        },
        messages: {
            required: 'Este campo é obrigatório',
            invalidEmail: 'Email inválido',
            invalidPhone: 'Telefone inválido (mínimo 10 dígitos)',
            invalidCNPJ: 'CNPJ inválido',
            shortName: 'Nome deve ter pelo menos 2 caracteres',
            shortCity: 'Digite uma cidade válida',
            shortMessage: 'Mensagem muito curta (mínimo 10 caracteres)'
        }
    },
    
    // Configurações de SEO
    seo: {
        title: 'Logyc Contabilidade - Seu Sucesso é Nossa Prioridade',
        description: 'Especialistas em abertura de empresas, gestão fiscal, departamento pessoal e consultoria contábil. Atendemos todo o Brasil com excelência.',
        keywords: 'contabilidade, abertura empresa, MEI, simples nacional, departamento pessoal, gestão fiscal, Curitiba, Brasil',
        author: 'Logyc Contabilidade',
        ogImage: '/logo.png'
    },
    
    // Configurações de analytics
    analytics: {
        googleAnalyticsId: '', // Configurar quando disponível
        facebookPixelId: '', // Configurar quando disponível
        events: {
            formSubmit: 'form_submit',
            calculatorUsage: 'calculator_usage',
            whatsappClick: 'whatsapp_click',
            buttonClick: 'button_click',
            pageView: 'page_view'
        }
    },
    
    // Configurações de notificações
    notifications: {
        duration: {
            success: 4000,
            error: 6000,
            warning: 5000,
            info: 4000
        },
        position: 'top-right'
    },
    
    // Configurações de acessibilidade
    accessibility: {
        enableHighContrast: true,
        enableKeyboardNavigation: true,
        enableScreenReader: true,
        shortcuts: {
            home: 'Alt+H',
            calculator: 'Alt+C',
            switchAccounting: 'Alt+T',
            highContrast: 'Alt+C'
        }
    },
    
    // URLs das páginas
    pages: {
        home: '/',
        help: '/como-podemos-ajudar.html',
        switchAccounting: '/troca-contabilidade.html',
        calculator: '/calculadora.html'
    },
    
    // Configurações de performance
    performance: {
        enableLazyLoading: true,
        enableServiceWorker: true,
        enableCache: true,
        cacheVersion: 'v1',
        preloadResources: [
            '/styles.css',
            '/forms.css',
            '/logo.png'
        ]
    },
    
    // Textos padrão
    texts: {
        loading: {
            default: 'Carregando...',
            form: 'Enviando...',
            calculator: 'Calculando...',
            page: 'Carregando página...'
        },
        success: {
            formSubmit: 'Formulário enviado com sucesso!',
            calculation: 'Cálculo realizado com sucesso!'
        },
        errors: {
            network: 'Erro de conexão. Tente novamente.',
            validation: 'Por favor, corrija os erros no formulário.',
            generic: 'Ocorreu um erro. Tente novamente.'
        }
    }
};

// Funções utilitárias baseadas na configuração
window.LogycUtils = {
    // Gerar URL do WhatsApp
    getWhatsAppUrl: function(message = 'default') {
        const config = window.LogycConfig.whatsapp;
        const messageText = config.messages[message] || config.messages.default;
        return `${config.baseUrl}${config.number}?text=${encodeURIComponent(messageText)}`;
    },
    
    // Formatar telefone
    formatPhone: function(phone) {
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.length === 11) {
            return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
        } else if (cleaned.length === 10) {
            return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
        }
        return phone;
    },
    
    // Formatar CNPJ
    formatCNPJ: function(cnpj) {
        const cleaned = cnpj.replace(/\D/g, '');
        return cleaned.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
    },
    
    // Formatar moeda
    formatCurrency: function(value) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(value);
    },
    
    // Obter configuração da calculadora
    getCalculatorConfig: function(type) {
        return window.LogycConfig.calculator.types[type];
    },
    
    // Validar email
    isValidEmail: function(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    },
    
    // Validar telefone
    isValidPhone: function(phone) {
        const cleaned = phone.replace(/\D/g, '');
        const config = window.LogycConfig.forms.validation;
        return cleaned.length >= config.minPhoneLength && cleaned.length <= config.maxPhoneLength;
    },
    
    // Rastrear evento
    trackEvent: function(eventName, parameters = {}) {
        if (window.trackEvent) {
            window.trackEvent(eventName, parameters);
        }
    }
};

// Aplicar configurações globais
document.addEventListener('DOMContentLoaded', function() {
    // Aplicar cores CSS
    const root = document.documentElement;
    const colors = window.LogycConfig.colors;
    
    Object.keys(colors).forEach(colorName => {
        root.style.setProperty(`--${colorName.replace(/([A-Z])/g, '-$1').toLowerCase()}`, colors[colorName]);
    });
    
    // Configurar título da página se não estiver definido
    if (!document.title || document.title === '') {
        document.title = window.LogycConfig.seo.title;
    }
    
    // Log de inicialização
    console.log('Logyc Contabilidade - Sistema inicializado', {
        version: '1.0.0',
        features: [
            'WhatsApp Integration',
            'Advanced Forms',
            'Calculator',
            'PWA',
            'SEO Optimized',
            'Accessibility',
            'Performance Optimized'
        ]
    });
});
